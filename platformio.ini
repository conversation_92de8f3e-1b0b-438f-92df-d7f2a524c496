[env:m5stack-atoms3r]
platform = espressif32@6.3.2
board = m5stack-atoms3
framework = arduino

; 基本配置 - 使用更安全的设置
board_build.arduino.memory_type = qio_qspi
board_build.partitions = default.csv
board_build.filesystem = littlefs

; 编译标志 - 最小化设置
build_flags =
    -DESP32S3
    -DCORE_DEBUG_LEVEL=1
    -DARDUINO_USB_CDC_ON_BOOT=1

; 库依赖
lib_deps =
    esp32-camera@^2.0.4
    bblanchon/ArduinoJson@^7.0.4

; 监视器配置
monitor_speed = 115200
monitor_encoding = utf-8
monitor_eol = LF
monitor_echo = yes
monitor_filters = esp32_exception_decoder

; 上传配置
upload_speed = 921600
; upload_port = COM15
; monitor_port = COM15

; 调试配置
debug_tool = esp-prog
debug_init_break = tbreak setup
